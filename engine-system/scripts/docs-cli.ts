#!/usr/bin/env tsx

import * as fs from 'fs';
import * as path from 'path';
import { <PERSON>downMerger, MergeOptions } from './merge-docs.js';
import { SiteGenerator, SiteOptions } from './generate-site.js';

interface CLIOptions {
  command: 'merge' | 'site' | 'both';
  configPath: string;
  outputPath?: string;
  outputDir?: string;
  format?: string[];
  theme?: string;
  includeTableOfContents?: boolean;
  includeSectionNumbers?: boolean;
  includeFileNumbers?: boolean;
  includeSourceLinks?: boolean;
  includeLastModified?: boolean;
  includeSearch?: boolean;
  verbose?: boolean;
  watch?: boolean;
}

class DocumentationCLI {
  private options: CLIOptions;

  constructor(options: CLIOptions) {
    this.options = options;
  }

  /**
   * Execute the CLI command
   */
  async execute(): Promise<void> {
    if (this.options.verbose) {
      console.log('📋 CLI Options:', this.options);
    }

    switch (this.options.command) {
      case 'merge':
        await this.executeMerge();
        break;
      case 'site':
        await this.executeSite();
        break;
      case 'both':
        await this.executeMerge();
        await this.executeSite();
        break;
      default:
        throw new Error(`Unknown command: ${this.options.command}`);
    }

    if (this.options.watch) {
      await this.startWatchMode();
    }
  }

  /**
   * Execute merge command
   */
  private async executeMerge(): Promise<void> {
    console.log('📄 Starting documentation merge...');

    const mergeOptions: MergeOptions = {
      configPath: this.options.configPath,
      outputPath: this.options.outputPath,
      includeTableOfContents: this.options.includeTableOfContents,
      includeSectionNumbers: this.options.includeSectionNumbers,
      includeFileNumbers: this.options.includeFileNumbers,
      includeSourceLinks: this.options.includeSourceLinks,
      includeLastModified: this.options.includeLastModified
    };

    const merger = new MarkdownMerger(mergeOptions);
    const stats = merger.getStats();

    if (this.options.verbose) {
      console.log(`📊 Documentation stats:`);
      console.log(`   Sections: ${stats.sections}`);
      console.log(`   Files: ${stats.files}`);
      console.log(`   Total size: ${(stats.totalSize / 1024).toFixed(1)} KB`);
    }

    await merger.merge();
    console.log('✅ Documentation merge completed!');
  }

  /**
   * Execute site generation command
   */
  private async executeSite(): Promise<void> {
    console.log('🌐 Starting documentation site generation...');

    const siteOptions: SiteOptions = {
      configPath: this.options.configPath,
      outputDir: this.options.outputDir,
      theme: this.options.theme,
      includeSearch: this.options.includeSearch,
      includeSourceLinks: this.options.includeSourceLinks,
      includeLastModified: this.options.includeLastModified
    };

    const generator = new SiteGenerator(siteOptions);
    await generator.generate();
    console.log('✅ Documentation site generation completed!');
  }

  /**
   * Start watch mode for automatic regeneration
   */
  private async startWatchMode(): Promise<void> {
    console.log('👀 Starting watch mode...');
    console.log('Press Ctrl+C to stop watching');

    const config = JSON.parse(fs.readFileSync(this.options.configPath, 'utf-8'));
    const rootDir = path.dirname(this.options.configPath);
    
    // Collect all files to watch
    const filesToWatch = [this.options.configPath];
    
    for (const section of config.structure.sections) {
      for (const file of section.files) {
        const filePath = path.join(rootDir, file.file);
        if (fs.existsSync(filePath)) {
          filesToWatch.push(filePath);
        }
      }
    }

    if (this.options.verbose) {
      console.log(`📁 Watching ${filesToWatch.length} files for changes...`);
    }

    // Simple file watching implementation
    const watchedFiles = new Map<string, number>();
    
    // Initialize file modification times
    for (const file of filesToWatch) {
      try {
        const stats = fs.statSync(file);
        watchedFiles.set(file, stats.mtime.getTime());
      } catch (error) {
        console.warn(`⚠️  Could not watch file: ${file}`);
      }
    }

    // Check for changes every 2 seconds
    const checkInterval = setInterval(async () => {
      let hasChanges = false;
      
      for (const file of filesToWatch) {
        try {
          const stats = fs.statSync(file);
          const currentTime = stats.mtime.getTime();
          const lastTime = watchedFiles.get(file) || 0;
          
          if (currentTime > lastTime) {
            console.log(`📝 File changed: ${path.relative(process.cwd(), file)}`);
            watchedFiles.set(file, currentTime);
            hasChanges = true;
          }
        } catch (error) {
          // File might have been deleted or moved
          console.warn(`⚠️  Could not check file: ${file}`);
        }
      }
      
      if (hasChanges) {
        console.log('🔄 Regenerating documentation...');
        try {
          await this.execute();
          console.log('✅ Documentation updated!');
        } catch (error) {
          console.error('❌ Error during regeneration:', error);
        }
      }
    }, 2000);

    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n👋 Stopping watch mode...');
      clearInterval(checkInterval);
      process.exit(0);
    });
  }

  /**
   * Validate CLI options
   */
  static validateOptions(options: CLIOptions): void {
    if (!fs.existsSync(options.configPath)) {
      throw new Error(`Configuration file not found: ${options.configPath}`);
    }

    if (options.command === 'merge' && options.outputPath) {
      const outputDir = path.dirname(options.outputPath);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }
    }

    if (options.command === 'site' && options.outputDir) {
      if (!fs.existsSync(options.outputDir)) {
        fs.mkdirSync(options.outputDir, { recursive: true });
      }
    }
  }
}

/**
 * Parse command line arguments
 */
function parseArguments(): CLIOptions {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args.includes('--help') || args.includes('-h')) {
    showHelp();
    process.exit(0);
  }

  const options: CLIOptions = {
    command: 'both',
    configPath: 'docs-config.json',
    includeTableOfContents: true,
    includeSectionNumbers: true,
    includeFileNumbers: true,
    includeSourceLinks: true,
    includeLastModified: true,
    includeSearch: true,
    verbose: false,
    watch: false
  };

  // Parse command
  if (args[0] && !args[0].startsWith('-')) {
    const command = args[0];
    if (['merge', 'site', 'both'].includes(command)) {
      options.command = command as 'merge' | 'site' | 'both';
      args.shift(); // Remove command from args
    }
  }

  // Parse config file
  if (args[0] && !args[0].startsWith('-')) {
    options.configPath = args[0];
    args.shift();
  }

  // Parse flags
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    switch (arg) {
      case '--output':
      case '-o':
        options.outputPath = args[++i];
        break;
      case '--output-dir':
      case '-d':
        options.outputDir = args[++i];
        break;
      case '--theme':
        options.theme = args[++i];
        break;
      case '--no-toc':
        options.includeTableOfContents = false;
        break;
      case '--no-section-numbers':
        options.includeSectionNumbers = false;
        break;
      case '--no-file-numbers':
        options.includeFileNumbers = false;
        break;
      case '--no-source-links':
        options.includeSourceLinks = false;
        break;
      case '--no-last-modified':
        options.includeLastModified = false;
        break;
      case '--no-search':
        options.includeSearch = false;
        break;
      case '--verbose':
      case '-v':
        options.verbose = true;
        break;
      case '--watch':
      case '-w':
        options.watch = true;
        break;
      default:
        if (arg.startsWith('-')) {
          console.warn(`⚠️  Unknown option: ${arg}`);
        }
    }
  }

  return options;
}

/**
 * Show help message
 */
function showHelp(): void {
  console.log(`
📚 Documentation Generator CLI

Usage: tsx docs-cli.ts [command] [config-file] [options]

Commands:
  merge                   Generate merged markdown document only
  site                    Generate documentation site only  
  both                    Generate both merged docs and site (default)

Options:
  --output, -o <file>     Output file for merged docs
  --output-dir, -d <dir>  Output directory for site
  --theme <theme>         Theme for site generation
  --no-toc               Disable table of contents
  --no-section-numbers   Disable section numbering
  --no-file-numbers      Disable file numbering
  --no-source-links      Disable source file links
  --no-last-modified     Disable last modified timestamps
  --no-search            Disable search functionality
  --verbose, -v          Enable verbose output
  --watch, -w            Watch for changes and regenerate
  --help, -h             Show this help message

Examples:
  tsx docs-cli.ts                                    # Generate both with defaults
  tsx docs-cli.ts merge docs-config.json            # Generate merged docs only
  tsx docs-cli.ts site docs-config.json --no-search # Generate site without search
  tsx docs-cli.ts both docs-config.json --watch     # Generate both and watch for changes
  tsx docs-cli.ts merge --output DOCS.md --verbose  # Custom output with verbose logging

Configuration:
  The CLI uses a JSON configuration file (default: docs-config.json) to define
  the documentation structure, styling, and output options.
`);
}

// Main execution
async function main() {
  try {
    const options = parseArguments();
    DocumentationCLI.validateOptions(options);
    
    const cli = new DocumentationCLI(options);
    await cli.execute();
    
    if (!options.watch) {
      console.log('🎉 All documentation generation tasks completed successfully!');
    }
  } catch (error) {
    console.error('❌ Error:', error instanceof Error ? error.message : error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { DocumentationCLI, CLIOptions };
